import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import TopUpContent from "./TopUpContent";
import { PRICING } from "~/constants/currency";

interface TopUpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  subtitle?: string;
}

export default function TopUpDialog({
  open,
  onOpenChange,
  title = "Unlock subsequent episodes",
  subtitle = `Price : ${PRICING.UNLOCK_EPISODE_COST} Coins to unlock this subsequent episodes`,
}: TopUpDialogProps) {
  // Add handler to stop propagation of touch events but allow normal touch interactions
  const handleDialogInteraction = (e: React.TouchEvent | React.MouseEvent) => {
    // Only stop propagation to prevent video feed swipe navigation
    // Don't prevent default to allow normal touch interactions like tapping buttons
    e.stopPropagation();
  };

  // Specific handler for the dialog content area to prevent swipe navigation while allowing normal interactions
  const handleContentInteraction = (e: React.TouchEvent | React.MouseEvent) => {
    const target = e.target as Element;

    // Check if the event is coming from a nested dialog (LoginDialog, RegisterDialog, etc.)
    // These dialogs have higher z-index and should not be interfered with
    const isFromNestedDialog =
      target.closest('[class*="z-[7"]') || target.closest('[class*="z-[8"]');

    // Check if the event is coming from interactive elements (buttons, inputs, etc.)
    const isInteractiveElement = target.closest(
      'button, input, select, textarea, a, [role="button"]'
    );

    // Only prevent events if they're not from a nested dialog or interactive element
    if (!isFromNestedDialog && !isInteractiveElement) {
      // Stop event propagation to prevent video feed swipe navigation
      e.stopPropagation();
      // Don't prevent default to allow normal touch interactions
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay
          className="fixed inset-0 bg-black/70 z-[60] data-[state=open]:animate-fadeIn data-[state=closed]:animate-fadeOut"
          // style={{ touchAction: "none" }}
        />
        <Dialog.Content
          className="fixed inset-0 z-[61] overflow-y-auto outline-none"
          style={{ touchAction: "pan-y" }}
          onTouchStart={handleDialogInteraction}
        >
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-[#1F2937] rounded-2xl shadow-2xl max-h-[80vh] lg:max-h-full overflow-y-auto text-white relative">
              {/* Close button for dialog */}

              <Dialog.Close className="absolute top-4 right-4 text-gray-400 hover:text-white z-10">
                <X size={24} />
              </Dialog.Close>

              {/* TopUp Content with event handling wrapper */}
              <div
                onTouchStart={handleContentInteraction}
                onTouchMove={handleContentInteraction}
              >
                <TopUpContent title={title} subtitle={subtitle} isInDialog />
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
