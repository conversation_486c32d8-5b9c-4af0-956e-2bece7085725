import { Loader } from "lucide-react";

interface LoadingSpinnerProps {
  text?: string;
  size?: number;
  className?: string;
  fullScreen?: boolean;
}

export default function LoadingSpinner({
  text,
  size = 32,
  className = "",
  fullScreen = false,
}: LoadingSpinnerProps) {
  const containerClass = fullScreen
    ? "fixed inset-0 flex items-center justify-center bg-black/80 z-50"
    : "flex flex-col items-center justify-center p-4";
  // Use provided text or fallback to translation
  const displayText = text;
  return (
    <div className={`${containerClass} ${className}`}>
      <div className="text-center">
        <Loader
          className="h-8 w-8 md:h-12 md:w-12 text-brand-red animate-spin mx-auto"
          size={size}
        />
        {/* {displayText && (
          <p className="mt-4 text-white text-sm md:text-lg">{displayText}</p>
        )}{" "} */}
      </div>
    </div>
  );
}
