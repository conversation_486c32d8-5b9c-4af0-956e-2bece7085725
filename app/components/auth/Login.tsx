import { Link, useNavigate } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { createBaseApi } from "~/utils/base-service";
import { LoginPayload } from "~/types/index";
import { useUser } from "~/context/auth-context";
import { useForm, SubmitHandler } from "react-hook-form";
import { useTranslation } from "react-i18next";
import SocialAuth from "~/components/auth/SocialAuth";

interface LoginComponentProps {
  initialStep?: "emailEntry" | "passwordEntry";
  onOpenRegister?: () => void;
  onOpenForgotPassword?: () => void;
}

interface LoginFormInputs {
  username: string;
  password: string;
  smsCode?: string;
  remember: boolean;
}

export default function LoginComponent(props: LoginComponentProps) {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<
    "emailEntry" | "passwordEntry"
  >(props.initialStep || "emailEntry");
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const navigate = useNavigate();
  const api = createBaseApi();
  const { loginUser } = useUser();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<LoginFormInputs>({
    defaultValues: {
      username: "",
      password: "",
      smsCode: "",
      remember: false,
    },
  });

  // Watch the username field to use its value
  const username = watch("username");

  const handleContinueWithEmail = () => {
    if (username && username.trim() !== "") {
      setCurrentStep("passwordEntry");
      setFormError(null);
    } else {
      setFormError(t("auth.login.errors.enterUsername"));
    }
  };

  const handleBackToEmail = () => {
    setCurrentStep("emailEntry");
    setFormError(null);
  };

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    if (currentStep === "passwordEntry") {
      setIsLoading(true);
      setFormError(null);
      try {
        const payload: LoginPayload = {
          username: data.username,
          password: data.password,
          smsCode: data.smsCode,
        };
        const response = await api.login(payload);

        if (response.ok && response.data) {
          await loginUser(response.data);
          console.log("Login successful, navigating...");

          // Check for redirect URL and navigate accordingly
          const redirectUrl = localStorage.getItem("login_redirect_url");
          if (redirectUrl) {
            console.log("Redirecting to", redirectUrl);
            navigate(redirectUrl);
            localStorage.removeItem("login_redirect_url");
          } else {
            navigate("/");
          }
        } else {
          setFormError(response.msg || t("auth.login.errors.loginFailed"));
        }
      } catch (err: any) {
        console.error("Login error:", err);
        setFormError(err.message || t("auth.login.errors.unexpectedError"));
      }
      setIsLoading(false);
    }
  };

  // Full page version with background
  return (
    <>
      {/* Content Area Wrapper - bottom for mobile, center for desktop */}
      <div className="relative z-10 flex flex-col flex-grow justify-end md:justify-center">
        {/* Form Card - rounded top for mobile, rounded all for desktop */}
        <div className="w-full max-w-md mx-auto bg-body-bg rounded-t-3xl md:rounded-2xl shadow-2xl lg:shadow-none p-8 space-y-6">
          {/* Step-specific Headers */}
          {currentStep === "emailEntry" ? (
            <></>
          ) : (
            <div className="w-full flex items-center justify-center relative mb-0">
              {!props.initialStep && (
                <button
                  onClick={handleBackToEmail}
                  className="absolute left-0 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 -ml-2 sm:-ml-3"
                >
                  <ArrowLeft size={24} />
                </button>
              )}
              <h2 className="text-lg sm:text-xl font-semibold text-white">
                {t("auth.login.signIn")}
              </h2>
            </div>
          )}

          <form className="w-full space-y-5" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label
                htmlFor="username"
                className="block text-xs font-medium text-gray-400 mb-1"
              >
                {t("auth.login.usernameOrEmail")}
              </label>
              <Input
                type="text"
                id="username"
                placeholder={t("auth.login.usernameOrEmailPlaceholder")}
                {...register("username", {
                  required: t("auth.login.errors.usernameRequired"),
                })}
                disabled={isLoading}
              />
              {errors.username && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.username.message}
                </p>
              )}
            </div>

            <AnimatePresence initial={false}>
              {currentStep === "passwordEntry" && (
                <motion.div
                  key="passwordSection"
                  initial={{ opacity: 0, height: 0, marginTop: 0 }}
                  animate={{ opacity: 1, height: "auto", marginTop: "1.25rem" }}
                  exit={{ opacity: 0, height: 0, marginTop: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="overflow-hidden"
                >
                  <div className="space-y-5">
                    <div>
                      <label
                        htmlFor="password"
                        className="block text-xs font-medium text-gray-400 mb-1"
                      >
                        {t("auth.login.password")}
                      </label>
                      <Input
                        type="password"
                        id="password"
                        placeholder={t("auth.login.passwordPlaceholder")}
                        {...register("password", {
                          required: t("auth.login.errors.passwordRequired"),
                          minLength: {
                            value: 6,
                            message: t("auth.login.errors.passwordMinLength"),
                          },
                        })}
                        isToggleablePassword
                        disabled={isLoading}
                      />
                      {errors.password && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.password.message}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <label
                        htmlFor="remember"
                        className="flex items-center text-gray-400 cursor-pointer"
                      >
                        <input
                          id="remember"
                          type="checkbox"
                          className="h-4 w-4 text-red-600 border-gray-500 rounded bg-gray-700 focus:ring-red-500 mr-2"
                          {...register("remember")}
                        />
                        {t("auth.login.rememberMe")}
                      </label>
                      {props.onOpenForgotPassword ? (
                        <button
                          onClick={props.onOpenForgotPassword}
                          className="font-medium text-red-500 hover:text-red-400"
                        >
                          {t("auth.login.forgotPassword")}
                        </button>
                      ) : (
                        <Link
                          to="/forgot-password"
                          className="font-medium text-red-500 hover:text-red-400"
                        >
                          {t("auth.login.forgotPassword")}
                        </Link>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {currentStep === "emailEntry" ? (
              <>
                <Button
                  type="button"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  onClick={handleContinueWithEmail}
                  disabled={isLoading}
                >
                  {t("auth.login.continueWithEmail")}
                </Button>
                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}
              </>
            ) : (
              <>
                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base"
                  disabled={isLoading}
                >
                  {isLoading
                    ? t("auth.login.signingIn")
                    : t("auth.login.signIn")}
                </Button>
                {formError && (
                  <p className="text-red-500 text-xs mt-2 text-center">
                    {formError}
                  </p>
                )}
              </>
            )}
          </form>

          <SocialAuth isLoading={isLoading} />

          <p className="text-center text-xs text-gray-400 pt-1 sm:pt-2">
            {t("auth.login.dontHaveAccount")}{" "}
            {props.onOpenRegister ? (
              <button
                onClick={props.onOpenRegister}
                className="font-medium text-red-500 hover:text-red-400"
              >
                {t("auth.login.signUp")}
              </button>
            ) : (
              <Link
                to="/register"
                state={{ animateRegister: true }}
                className="font-medium text-red-500 hover:text-red-400"
              >
                {t("auth.login.signUp")}
              </Link>
            )}
          </p>
        </div>
      </div>
    </>
  );
}
