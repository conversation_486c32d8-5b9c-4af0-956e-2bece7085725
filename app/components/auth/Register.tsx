import { Link, useLocation, useNavigate } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { createBaseApi } from "~/utils/base-service";
import { RegisterPayload } from "~/types/index";
import OtpInput from "./OtpInput";
import { useForm, SubmitHandler } from "react-hook-form";
import { useTranslation } from "react-i18next";

interface RegisterComponentProps {
  // initialStep is no longer used as we always start with form
  onOpenLogin?: () => void;
}

// Define the possible steps for registration
type RegisterStep = "formEntry" | "otpEntry";

interface RegisterFormInputs {
  nickname: string;
  username: string;
  password: string;
  confirmPassword: string;
}

export default function RegisterComponent(props: RegisterComponentProps) {
  const { t } = useTranslation();
  const location = useLocation();
  const shouldAnimateFormCard = location.state?.animateRegister === true;
  const navigate = useNavigate();
  const api = createBaseApi();
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  const [currentStep, setCurrentStep] = useState<RegisterStep>("formEntry");

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormInputs>({
    defaultValues: {
      nickname: "",
      username: "",
      password: "",
      confirmPassword: "",
    },
  });

  // OTP state
  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // For password confirmation validation
  const password = watch("password");

  // Handler for submitting the initial registration form
  const onSubmit: SubmitHandler<RegisterFormInputs> = async (data) => {
    if (data.password !== data.confirmPassword) {
      setFormError(t("auth.register.errors.passwordsDoNotMatch"));
      return;
    }
    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Send verification code to the user's email
      const sendCodePayload = { username: data.username };
      const response = await api.sendCode(sendCodePayload);

      if (response.ok) {
        setFormSuccess(t("auth.register.success.verificationCodeSent"));
        setCurrentStep("otpEntry"); // Move to OTP step
      } else {
        setFormError(response.msg || t("auth.register.errors.sendCodeFailed"));
      }
    } catch (err: any) {
      console.error("Send code error:", err);
      setFormError(err.message || t("auth.register.errors.sendCodeFailed"));
    }
    setIsLoading(false);
  };

  // Handler for verifying OTP and completing registration
  const handleOtpVerifyAndRegister = async () => {
    if (otp.length !== 4) {
      // Basic OTP validation
      setFormError(t("auth.register.errors.validOtp"));
      return;
    }
    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);
    try {
      const formValues = watch();
      const payload: RegisterPayload = {
        nickname: formValues.nickname,
        username: formValues.username,
        password: formValues.password,
        confirmPassword: formValues.confirmPassword,
        code: otp, // Pass the entered OTP as the code
      };
      const response = await api.register(payload);

      if (response.ok && response.data) {
        setFormSuccess(
          response.data || t("auth.register.success.registrationSuccessful")
        );
        setTimeout(() => {
          if (props.onOpenLogin) {
            props.onOpenLogin();
          } else {
            navigate("/login");
          }
        }, 2000);
      } else {
        setFormError(
          response.msg || t("auth.register.errors.registrationFailed")
        );
      }
    } catch (err: any) {
      console.error("Registration with OTP error:", err);
      setFormError(err.message || t("auth.register.errors.registrationFailed"));
    }
    setIsLoading(false);
  };

  const handleBackToForm = () => {
    setCurrentStep("formEntry");
    setFormError(null);
    setFormSuccess(null);
    setOtp(""); // Clear OTP input when going back
  };

  const handleResendOtp = async () => {
    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      const formValues = watch();
      // Resend verification code to the user's email
      const sendCodePayload = { username: formValues.username };
      const response = await api.sendCode(sendCodePayload);

      if (response.ok) {
        setFormSuccess(t("auth.register.success.newVerificationCodeSent"));
      } else {
        setFormError(
          response.msg || t("auth.register.errors.resendCodeFailed")
        );
      }
    } catch (err: any) {
      console.error("Resend code error:", err);
      setFormError(err.message || t("auth.register.errors.resendCodeFailed"));
    }
    setIsLoading(false);
  };

  return (
    <>
      {/* Content Area Wrapper with full dvh */}
      <div className="relative flex flex-col flex-grow justify-start md:justify-center">
        {/* MODIFIED: Form Card to motion.div and updated classes/animation */}
        <motion.div
          className="w-full max-w-md mx-auto bg-body-bg lg:rounded-t-3xl md:rounded-3xl shadow-2xl h-dvh lg:h-full p-4 overflow-y-auto"
          initial={{ y: shouldAnimateFormCard ? "100%" : 0 }}
          animate={{ y: 0 }}
          transition={
            shouldAnimateFormCard
              ? { duration: 0.6, ease: "easeInOut" }
              : { duration: 0 }
          }
        >
          {/* Header - Positioned at the top */}
          <div className="w-full flex items-center justify-center relative py-4 px-6 border-gray-700">
            {currentStep === "formEntry" ? (
              <Link
                to="/login"
                className="absolute left-6 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 lg:hidden"
              >
                <ArrowLeft size={24} />
              </Link>
            ) : (
              <button
                onClick={handleBackToForm}
                className="absolute left-6 top-1/2 -translate-y-1/2 text-white hover:text-gray-300"
              >
                <ArrowLeft size={24} />
              </button>
            )}
            <h2 className="text-lg sm:text-xl font-semibold text-white">
              {currentStep === "formEntry"
                ? t("auth.register.signUp")
                : t("auth.register.verification")}
            </h2>
          </div>

          <div className="p-6 sm:p-8 space-y-6">
            {currentStep === "formEntry" && (
              <>
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold mb-1">
                    {t("auth.register.completeAccount")}
                  </h3>
                  <p className="text-sm text-gray-400">
                    {t("auth.register.enterDetails")}
                  </p>
                </div>

                <form
                  className="w-full space-y-5"
                  onSubmit={handleSubmit(onSubmit)}
                >
                  <div>
                    <label
                      htmlFor="nickname"
                      className="block text-xs font-medium text-gray-400 mb-1"
                    >
                      {t("auth.register.nickname")}
                    </label>
                    <Input
                      type="text"
                      id="nickname"
                      placeholder={t("auth.register.nicknamePlaceholder")}
                      {...register("nickname", {
                        required: t("auth.register.errors.nicknameRequired"),
                      })}
                      disabled={isLoading}
                    />
                    {errors.nickname && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.nickname.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="username"
                      className="block text-xs font-medium text-gray-400 mb-1"
                    >
                      {t("auth.register.email")}
                    </label>
                    <Input
                      type="email"
                      id="username"
                      placeholder={t("auth.register.emailPlaceholder")}
                      {...register("username", {
                        required: t("auth.register.errors.emailRequired"),
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: t("auth.register.errors.invalidEmail"),
                        },
                      })}
                      disabled={isLoading}
                    />
                    {errors.username && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.username.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="password"
                      className="block text-xs font-medium text-gray-400 mb-1"
                    >
                      {t("auth.register.password")}
                    </label>
                    <Input
                      type="password"
                      id="password"
                      placeholder={t("auth.register.passwordPlaceholder")}
                      {...register("password", {
                        required: t("auth.register.errors.passwordRequired"),
                        minLength: {
                          value: 6,
                          message: t("auth.register.errors.passwordMinLength"),
                        },
                      })}
                      isToggleablePassword
                      disabled={isLoading}
                    />
                    {errors.password && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.password.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="confirmPassword"
                      className="block text-xs font-medium text-gray-400 mb-1"
                    >
                      {t("auth.register.confirmPassword")}
                    </label>
                    <Input
                      type="password"
                      id="confirmPassword"
                      placeholder={t(
                        "auth.register.confirmPasswordPlaceholder"
                      )}
                      {...register("confirmPassword", {
                        required: t(
                          "auth.register.errors.confirmPasswordRequired"
                        ),
                        validate: (value) =>
                          value === password ||
                          t("auth.register.errors.passwordsDoNotMatch"),
                      })}
                      isToggleablePassword
                      disabled={isLoading}
                    />
                    {errors.confirmPassword && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.confirmPassword.message}
                      </p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    variant="primary"
                    fullWidth
                    className="py-3 text-base"
                    disabled={isLoading}
                  >
                    {isLoading
                      ? t("auth.register.processing")
                      : t("auth.register.signUp")}
                  </Button>

                  {formError && (
                    <p className="text-red-500 text-xs mt-2 text-center">
                      {formError}
                    </p>
                  )}

                  {formSuccess && (
                    <p className="text-green-500 text-xs mt-2 text-center">
                      {formSuccess}
                    </p>
                  )}
                </form>

                <p className="text-center text-xs text-gray-400 pt-1 sm:pt-2">
                  {t("auth.register.alreadyHaveAccount")}{" "}
                  {props.onOpenLogin ? (
                    <button
                      onClick={props.onOpenLogin}
                      className="font-medium text-red-500 hover:text-red-400"
                    >
                      {t("auth.register.signIn")}
                    </button>
                  ) : (
                    <Link
                      to="/login"
                      className="font-medium text-red-500 hover:text-red-400"
                    >
                      {t("auth.register.signIn")}
                    </Link>
                  )}
                </p>
              </>
            )}

            {currentStep === "otpEntry" && (
              <div className="flex flex-col items-center justify-center text-center pt-4">
                <img
                  src="/images/otp-illustration.png"
                  alt="OTP Illustration"
                  className="w-32 h-32 sm:w-40 sm:h-40 mb-6"
                />
                {/* Placeholder image */}
                <h2 className="text-2xl sm:text-3xl font-semibold text-white mb-2">
                  {t("auth.register.enterVerificationCode")}
                </h2>
                <p className="text-sm text-gray-400 mb-8 max-w-xs">
                  {t("auth.register.verificationCodeSent")}{" "}
                  <span className="font-medium text-white">
                    {watch("username")}
                  </span>
                </p>
                <div className="w-full max-w-xs mb-8">
                  <OtpInput onChange={setOtp} disabled={isLoading} />
                </div>
                <Button
                  type="button"
                  variant="primary"
                  fullWidth
                  className="py-3 text-base mb-4 max-w-xs"
                  onClick={handleOtpVerifyAndRegister}
                  disabled={isLoading || otp.length !== 4}
                >
                  {isLoading
                    ? t("auth.register.verifying")
                    : t("auth.register.completeRegistration")}
                </Button>
                {formError && (
                  <p className="text-red-500 text-xs mb-4 text-center max-w-xs">
                    {formError}
                  </p>
                )}
                {formSuccess && (
                  <p className="text-green-500 text-xs mb-4 text-center max-w-xs">
                    {formSuccess}
                  </p>
                )}
                <p className="text-xs text-gray-400">
                  {t("auth.register.didntReceiveCode")}{" "}
                  <button
                    onClick={handleResendOtp}
                    disabled={isLoading}
                    className="font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t("auth.register.resendCode")}
                  </button>
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </>
  );
}
