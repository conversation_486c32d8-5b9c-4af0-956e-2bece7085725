import { Apple } from "lucide-react";
import { Button } from "~/components/ui/Button";
import { useTranslation } from "react-i18next";

// Google Icon component
const GoogleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
  </svg>
);

// Facebook Icon component
const FacebookIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M9.198 21.5h4v-8.01h3.604l.396-3.98h-4V7.5a1 1 0 0 1 1-1h3v-4h-3a5 5 0 0 0-5 5v2.01h-2l-.396 3.98h2.396v8.01Z" />
  </svg>
);

interface SocialAuthProps {
  onFacebookLogin?: () => void;
  onGoogleLogin?: () => void;
  onAppleLogin?: () => void;
  isLoading?: boolean;
}

export default function SocialAuth({
  onFacebookLogin,
  onGoogleLogin,
  onAppleLogin,
  isLoading = false,
}: SocialAuthProps) {
  const { t } = useTranslation();

  const handleFacebookLogin = () => {
    if (onFacebookLogin) {
      onFacebookLogin();
    } else {
      console.log("Facebook login not implemented");
      // TODO: Implement Facebook OAuth
    }
  };

  const handleGoogleLogin = () => {
    if (onGoogleLogin) {
      onGoogleLogin();
    } else {
      console.log("Google login not implemented");
      // TODO: Implement Google OAuth
    }
  };

  const handleAppleLogin = () => {
    if (onAppleLogin) {
      onAppleLogin();
    } else {
      console.log("Apple login not implemented");
      // TODO: Implement Apple OAuth
    }
  };

  return (
    <>
      {/* Divider */}
      <div className="flex items-center w-full py-1 sm:py-2">
        <hr className="flex-grow border-gray-600" />
        <span className="px-3 text-gray-400 text-xs">
          {t("auth.login.orContinueWith")}
        </span>
        <hr className="flex-grow border-gray-600" />
      </div>

      {/* Social Auth Buttons */}
      <div className="w-full space-y-3">
        <Button
          type="button"
          fullWidth
          className="bg-blue-600 hover:bg-blue-700 text-white py-3 text-sm"
          leftIcon={<FacebookIcon />}
          onClick={handleFacebookLogin}
          disabled={isLoading}
        >
          {t("auth.login.continueWithFacebook")}
        </Button>
        <Button
          type="button"
          fullWidth
          className="bg-gray-700 hover:bg-gray-600 text-white py-3 text-sm"
          leftIcon={<GoogleIcon />}
          onClick={handleGoogleLogin}
          disabled={isLoading}
        >
          {t("auth.login.continueWithGoogle")}
        </Button>
        <Button
          type="button"
          fullWidth
          variant="outline"
          className="border-gray-500 hover:bg-gray-700 text-white py-3 text-sm"
          leftIcon={<Apple size={20} />}
          onClick={handleAppleLogin}
          disabled={isLoading}
        >
          {t("auth.login.continueWithApple")}
        </Button>
      </div>
    </>
  );
}
