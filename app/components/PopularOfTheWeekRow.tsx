import { useNavigate } from "@remix-run/react";
import type { ContentItem } from "~/types/index";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/react-splide";
import "@splidejs/react-splide/css/core"; // Core CSS for Splide
import { useMediaQuery } from "~/hooks/useMediaQuery";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";

interface PopularOfTheWeekRowProps {
  title: string;
  items: ContentItem[];
}

const PopularOfTheWeekRow = ({ title, items }: PopularOfTheWeekRowProps) => {
  const { isDesktop } = useMediaQuery();
  const { navigateToVideo } = useVideoNavigation();

  if (!items || items.length === 0) {
    return null;
  }

  const splideOptions = {
    type: "slide",
    perMove: 1,
    pagination: false,
    arrows: isDesktop,
    breakpoints: {
      1280: {
        // lg and up (desktop)
        perPage: 3, // Show 5 items per page
        gap: "1.5rem",
      },
      1024: {
        // lg and up (desktop)
        perPage: 2, // Show 5 items per page
        gap: "1.5rem",
      },
      768: {
        // md
        perPage: 2,
        gap: "1.5rem",
      },
      640: {
        // sm - aiming for 1.5 items visible effect
        perPage: 1,
        gap: "1rem",
        padding: { right: "4rem" },
        trimSpace: false,
      },
    },
    perPage: 3,
    gap: "1rem",
    padding: { right: "4rem" },
    trimSpace: false,
  };

  // We will still display only the first 3 items, as per the component's design
  const displayItems = items.slice(0, 10);

  return (
    <div className="mb-8">
      <h3 className="text-white text-xl font-bold mb-4 px-4">{title}</h3>
      <div className="popular-of-the-week-splide">
        {" "}
        {/* Added a class for potential custom arrow styling */}
        <Splide options={splideOptions} hasTrack={false} aria-label={title}>
          <SplideTrack>
            {displayItems.map((item, index) => (
              <SplideSlide key={item.id}>
                <div
                  className="group cursor-pointer h-full first:ml-4"
                  onClick={() => navigateToVideo(item.id)}
                >
                  {/* Flex container for [Number+Image] and [Text Info] */}
                  <div className="flex items-center h-full space-x-3 sm:space-x-4">
                    {/* Part 1: Number and Image (maintaining their original relative layout) */}
                    <div className="flex items-center space-x-[-10px] sm:space-x-[-15px] shrink-0">
                      <div
                        className="text-[10rem]  lg:text-[12rem] font-bold text-stroke-2 text-stroke-gray-700 text-transparent relative z-0 select-none shrink-0"
                        style={{ WebkitTextStroke: "2px #4A5568" }}
                      >
                        {index + 1}
                      </div>
                      <div className="relative z-10 w-32 h-48 sm:w-40 sm:h-60 md:w-44 md:h-[264px] lg:w-48 lg:h-72 rounded-lg overflow-hidden shadow-xl transform transition-transform duration-300 group-hover:scale-105 bg-gray-800 shrink-0">
                        <img
                          src={item.thumbnailUrl}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      </div>
                    </div>

                    {/* Part 2: Video Info Text Block */}
                    <div className="text-white flex flex-col justify-center py-2 flex-1 min-w-0">
                      <h4 className="text-lg sm:text-xl md:text-2xl font-bold mb-1 sm:mb-1.5 truncate">
                        {item.name}
                      </h4>
                      <p className="text-xs sm:text-sm text-gray-300 mb-0.5 sm:mb-1">
                        Episodes {item.episodes}
                      </p>
                      <div className="flex items-center text-xs sm:text-sm text-gray-300 mb-0.5 sm:mb-1">
                        <span
                          role="img"
                          aria-label="genre"
                          className="mr-1.5 text-base"
                        >
                          🎬
                        </span>
                        {item.genre?.join(" • ") || "Unknown"}
                      </div>
                      {item.rating && (
                        <div className="flex items-center text-xs sm:text-sm">
                          <span
                            role="img"
                            aria-label="rating"
                            className="text-yellow-400 mr-1 text-base"
                          >
                            ★
                          </span>
                          <span className="text-white font-semibold">
                            {item.rating}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </SplideSlide>
            ))}
          </SplideTrack>
        </Splide>
      </div>
    </div>
  );
};

export default PopularOfTheWeekRow;
