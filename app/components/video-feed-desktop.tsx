import React, { useState, useCallback, useEffect, useRef } from "react";
import { useNavigate, useParams, useSearchParams } from "@remix-run/react";
import BaseVideoPlayer from "./base-video-player";
import { VideoData, EpisodeGridItem } from "~/types/videos";
import {
  Heart,
  MessageCircle,
  Share2,
  CircleArrowLeft,
  Star,
} from "lucide-react";
import { videoEvents, uiEvents } from "~/utils/analytics";
import EpisodeGrid from "./video/EpisodeGrid";
import { EpisodeItem } from "~/types/index";
import { useTranslation } from "react-i18next";
import { useToast } from "~/components/ui/toast-provider";

interface VideoFeedDesktopProps {
  videoData: VideoData;
  episodes: EpisodeItem[];
}

export default function VideoFeedDesktop({
  videoData,
  episodes,
}: VideoFeedDesktopProps) {
  const navigate = useNavigate();
  const [liked, setLiked] = useState(videoData.initialLiked || false);
  const progressMarkers = useRef<Set<number>>(new Set());
  const [searchParams, setSearchParams] = useSearchParams();
  const activeEpisodeId = searchParams.get("episodeId");
  const { t } = useTranslation();
  const { showToast } = useToast();

  useEffect(() => {
    if (
      videoData.contentType === "collection" &&
      episodes.length > 0 &&
      !activeEpisodeId
    ) {
      const firstEpisode = episodes[0];
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("episodeId", firstEpisode.id);
      setSearchParams(newSearchParams);
    }
  }, [
    videoData.contentType,
    episodes,
    activeEpisodeId,
    searchParams,
    setSearchParams,
  ]);

  useEffect(() => {
    progressMarkers.current = new Set();
  }, [videoData.id]);

  useEffect(() => {
    if (videoData) {
      videoEvents.play(
        videoData.id,
        videoData.title || t("video.unknownTitle")
      );
    }
  }, [videoData]);

  const handleClose = () => {
    uiEvents.closeVideo(
      videoData.id,
      videoData.title || t("video.unknownTitle")
    );
    navigate(-1);
  };

  const handleLike = () => {
    const newLikedState = !liked;
    setLiked(newLikedState);
    videoEvents.like(
      videoData.id,
      videoData.title || t("video.unknownTitle"),
      newLikedState
    );
  };

  const handleShare = () => {
    videoEvents.share(videoData.id, videoData.title || t("video.unknownTitle"));
    navigator.clipboard.writeText(window.location.href);
    showToast(t("toast.copiedToClipboard"));
  };

  const handleComment = () => {
    videoEvents.comment(
      videoData.id,
      videoData.title || t("video.unknownTitle")
    );
    console.log(`Comment on video ${videoData.id}`);
  };

  const handleVideoEnded = useCallback(() => {
    videoEvents.complete(
      videoData.id,
      videoData.title || t("video.unknownTitle")
    );
    if (episodes.length > 0 && videoData.contentType === "collection") {
      const currentEpisodeIndex = episodes.findIndex(
        (ep) => ep.id === activeEpisodeId
      );
      if (
        currentEpisodeIndex !== -1 &&
        currentEpisodeIndex < episodes.length - 1
      ) {
        const nextEpisode = episodes[currentEpisodeIndex + 1];
        const currentURL = new URL(window.location.href);
        currentURL.searchParams.set("episodeId", nextEpisode.id);
        setSearchParams(currentURL.searchParams);
      }
    }
  }, [videoData, episodes, activeEpisodeId, setSearchParams]);

  const handleEpisodeSelect = (episodeNumber: number) => {
    const episode = episodes.find((ep, index) => {
      const parsedNumber = ep.title ? parseInt(ep.title, 10) : index + 1;
      const epNumber = isNaN(parsedNumber) ? index + 1 : parsedNumber;
      return epNumber === episodeNumber;
    });

    if (episode) {
      videoEvents.selectEpisode(
        videoData.id,
        videoData.title || t("series.unknownTitle"),
        episodeNumber,
        episode.id
      );
      const currentURL = new URL(window.location.href);
      currentURL.searchParams.set("episodeId", episode.id);
      setSearchParams(currentURL.searchParams);
    }
  };

  const handleTimeUpdate = useCallback(
    (time: number) => {
      const video = document.querySelector("video");
      const duration = video?.duration || 0;

      if (duration > 0) {
        const percentage = (time / duration) * 100;
        const points = [25, 50, 75];
        points.forEach((point) => {
          if (percentage >= point && !progressMarkers.current.has(point)) {
            progressMarkers.current.add(point);
            videoEvents.progress(
              videoData.id,
              videoData.title || t("video.unknownTitle"),
              point
            );
          }
        });
      }
    },
    [videoData]
  );

  const handleTopUpClick = () => {
    console.log("Top-up clicked for video:", videoData.id);
    showToast(t("toast.topUpFeatureComingSoon"));
    // TODO: Implement top-up functionality
  };

  const generateEpisodeGrid = useCallback((): EpisodeGridItem[] => {
    if (!episodes.length) return [];
    return episodes.map((episode, index) => {
      const parsedNumber = episode.title
        ? parseInt(episode.title, 10)
        : index + 1;
      const episodeNumber = isNaN(parsedNumber) ? index + 1 : parsedNumber;
      return {
        number: episodeNumber,
        locked: episode.vip,
        active: episode.id === activeEpisodeId,
      };
    });
  }, [episodes, activeEpisodeId]);

  if (!videoData) {
    return <div>{t("video.loadingVideo")}</div>;
  }

  const episodeGridItems = generateEpisodeGrid();
  const isCollection = videoData.contentType === "collection";
  const hasEpisodes = episodeGridItems.length > 0;
  const selectedEpisode = episodes.find((ep) => ep.id === activeEpisodeId);

  return (
    <div className="fixed top-16 left-0 right-0 bottom-0 bg-black overflow-hidden">
      <div className="flex h-full">
        <div className="w-2/3 h-full relative">
          {selectedEpisode ? (
            <BaseVideoPlayer
              src={selectedEpisode.playUrl || ""}
              poster={videoData.posterUrl}
              autoPlay
              showControls={true}
              className="w-full h-full"
              isLocked={selectedEpisode.vip || videoData.locked}
              // isLocked={true}
              onEnded={handleVideoEnded}
              onPlay={() =>
                videoEvents.play(
                  videoData.id,
                  videoData.title || t("video.unknownTitle")
                )
              }
              onPause={() =>
                videoEvents.pause(
                  videoData.id,
                  videoData.title || t("video.unknownTitle")
                )
              }
              onTimeUpdate={handleTimeUpdate}
              subtitleLabel="English"
              subtitleUrl={selectedEpisode.subtitle || undefined}
              title={videoData.title}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-black text-white">
              <p>
                {episodes.length > 0
                  ? t("episode.loadingEpisode")
                  : t("episode.noEpisodesAvailable")}
              </p>
            </div>
          )}
        </div>

        <div className="w-1/3 h-full bg-body-bg text-white p-6 overflow-y-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-2">
              {videoData.title || t("video.defaultTitle")}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-neutral-400 mb-4">
              {/* TODO */}
              {/* <Star size={16} className="text-yellow-400 fill-yellow-400" />
              <span>7.5</span>
              <span>|</span>
              <span>Season 8</span>
              <span>•</span> */}
              <span>
                {t("common.episodesCap")}{" "}
                <span className="text-brand-red font-bold">
                  {episodeGridItems.length}
                </span>
              </span>
              {/* TODO */}
              {/* <span>•</span>
              <span>2025/06/23</span> */}
            </div>
            <div className="flex items-center space-x-6 mb-6">
              <button
                className="flex items-center space-x-1"
                onClick={handleShare}
              >
                <Share2 size={20} />
                <span>{t("common.share")}</span>
              </button>
            </div>
            <p className="text-sm text-gray-300 mb-6">
              {videoData.description || t("video.noDescription")}
            </p>
            <div className="flex flex-wrap gap-2 mb-6">
              {(videoData.tags || []).map((tag, idx) => (
                <span
                  key={idx}
                  className="bg-[#FFFFFF4D] text-white text-sm px-3 py-1 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {isCollection && hasEpisodes && (
            <div>
              <h2 className="text-xl font-bold mb-4">
                {t("common.episodesCap")} ({episodes.length})
              </h2>
              <EpisodeGrid episodes={episodes} videoData={videoData} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
