import React from "react";
import { useTranslation } from "react-i18next";
import { Play, Bookmark, Star, PlayCircle } from "lucide-react";
import { Button } from "~/components/ui/Button";
import type { ContentItem } from "~/types/index";
import { Splide, SplideSlide, SplideTrack } from "@splidejs/react-splide";
// Core Splide CSS is needed. Custom styles are in splide-custom.css, assumed to be globally loaded via root.tsx
import "@splidejs/react-splide/css/core";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";
import AddToWatchlistButton from "./ui/AddToWatchlistButton";

interface FeaturedContentProps {
  items: ContentItem[]; // Expect an array of items
}

export const FeaturedContent: React.FC<FeaturedContentProps> = ({ items }) => {
  const { t } = useTranslation();
  const { navigateToVideo } = useVideoNavigation();
  if (!items || items.length === 0) {
    return (
      <div className="relative w-full h-[500px] md:h-[550px] bg-gray-800 flex items-center justify-center text-white">
        <p>No featured content available.</p>
      </div>
    );
  }

  const splideOptions = {
    type: "loop", // Loop for continuous play
    perPage: 1,
    arrows: false,
    pagination: true,
    autoplay: true,
    interval: 5000,
    pauseOnHover: true,
    pauseOnFocus: true,
    rewind: true,
    classes: {
      pagination: "splide__pagination custom-pagination", // Apply custom pagination class
    },
  };

  return (
    <div className="relative">
      <Splide
        options={splideOptions}
        hasTrack={false}
        aria-label="Featured Content"
      >
        <SplideTrack>
          {items.map((item) => {
            const genres = item.genre || [];
            return (
              <SplideSlide key={item.id}>
                <div className="relative w-full h-[500px] md:h-[550px] text-white">
                  <img
                    src={item.thumbnailUrl}
                    alt={`${item.name} background`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
                  <div className="absolute bottom-0 p-4 md:p-8 w-full pb-20 ">
                    {" "}
                    {/* Increased pb for pagination */}
                    <div className="max-w-screen-2xl mx-auto">
                      {genres.length > 0 && genres[0] !== "N/A" && (
                        <div className="flex space-x-2 mb-3">
                          {genres.map((genre, index) => (
                            <span
                              key={index}
                              className="bg-[#FFFFFF4D] text-white text-sm px-3 py-1 rounded-full"
                            >
                              {genre}
                            </span>
                          ))}
                        </div>
                      )}
                      <h2 className="text-3xl md:text-4xl font-bold mb-2">
                        {item.name}
                      </h2>
                      <div className="flex flex-wrap items-center gap-x-2 gap-y-1 mb-3 text-sm text-neutral-300">
                        {item.rating && (
                          <div className="flex items-center">
                            <Star
                              size={16}
                              className="text-yellow-400 fill-yellow-400 mr-1"
                            />
                            <span>{item.rating.toFixed(1)}</span>
                          </div>
                        )}
                        {item.year && (
                          <>
                            {item.rating && (
                              <span className="text-neutral-500">|</span>
                            )}
                            <span>{item.year}</span>
                          </>
                        )}
                        {item.episodes && (
                          <>
                            {(item.rating || item.year) && (
                              <span className="text-neutral-500">|</span>
                            )}
                            <span>
                              {t("video.season", "Season")}{" "}
                              <span className="text-red-500 font-medium">
                                1
                              </span>{" "}
                              {/* Placeholder Season */}
                              {" • "}
                              {item.episodes} {t("video.episodes", "Episodes")}
                            </span>
                          </>
                        )}
                        {/* Assuming Jikan API might provide a release date like string for aired.string */}
                        {item.duration && !item.episodes && (
                          /* Show duration if no episodes, or could be a movie */ <>
                            {(item.rating || item.year) && (
                              <span className="text-neutral-500">|</span>
                            )}
                            <span>{item.duration}</span>
                          </>
                        )}
                      </div>
                      {item.description && (
                        <p className="text-sm text-neutral-200 mb-6 leading-relaxed max-h-24 overflow-hidden line-clamp-3 md:line-clamp-3">
                          {item.description}
                        </p>
                      )}
                      <div className="flex flex-row gap-4 mt-6">
                        <Button
                          leftIcon={<PlayCircle size={20} />}
                          variant="primary"
                          size="sm"
                          onClick={() => {
                            navigateToVideo(item.id);
                          }}
                        >
                          {t("featured.playNow", "Play Now")}
                        </Button>
                        <AddToWatchlistButton videoId={item.id} />
                      </div>
                    </div>
                  </div>
                </div>
              </SplideSlide>
            );
          })}
        </SplideTrack>
      </Splide>
    </div>
  );
};

export default FeaturedContent;
