import { useState, useEffect } from "react";
import {
  Play,
  ArrowLeft,
  Star,
  Lock,
  ChevronDown,
  ChevronUp,
  PlayCircle,
} from "lucide-react";
import {
  useNavigate,
  useLoaderData,
  useParams,
  MetaFunction,
  ClientLoaderFunctionArgs,
} from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useTranslation } from "react-i18next";

import { createBaseApi } from "~/utils/base-service";
import type {
  PostDetailsResponse,
  PostListResponse,
  EpisodeItem,
  HomeIndexResponse,
  HomeIndexItem,
  HomeIndexListItem,
} from "~/types/index";
import Button from "~/components/ui/Button";
import AddToWatchlistButton from "~/components/ui/AddToWatchlistButton";
import { episodeButtonStyles } from "~/styles/episodeButtonVariants";
import { cn } from "~/utils/cn";
import AppHeader from "~/components/AppHeader";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";
import { useAuthenticatedPostList } from "~/hooks/useAuthenticatedPostList";
import { useUser } from "~/context/auth-context";

import i18next from "~/i18next.server";
// --- Common getData function for both server and client loaders ---
async function getData(params: any, locale?: string) {
  const videoId = params.videoId;

  if (!videoId) {
    throw new Response("Not Found", {
      status: 404,
      statusText: "Missing video ID",
    });
  }

  try {
    // Use our API to fetch the video details
    const response: PostDetailsResponse = await createBaseApi(
      locale
    ).getPostDetails(videoId);

    if (!response.ok) {
      throw new Response("Not Found", {
        status: 404,
        statusText: `Video with ID ${videoId} not found`,
      });
    }

    // Fetch the episode list using the video ID as the collection ID
    const episodesResponse: PostListResponse = await createBaseApi(
      locale
    ).getPostList({
      collectionId: videoId,
    });

    // Fetch recommendations from the home index API
    const recommendationsResponse: HomeIndexResponse = await createBaseApi(
      locale
    ).getHomeIndex();

    // Extract recommendations from the home index response
    let recommendations: EpisodeItem[] = [];
    if (recommendationsResponse.ok && recommendationsResponse.data) {
      // Flatten all items from different sections into a single array
      const allItems: HomeIndexListItem[] =
        recommendationsResponse.data.flatMap(
          (section: HomeIndexItem) => section.list || []
        );

      // Convert HomeIndexListItem to EpisodeItem format
      recommendations = allItems
        .filter((item) => item.id !== videoId) // Filter out the current video
        .slice(0, 6) // Limit to 12 recommendations
        .map((item) => ({
          id: item.id,
          title: item.title,
          picture: item.picture,
          status: null,
          vip: false,
          gold: 0,
          topic: null,
          introduce: null,
          seoKeyword: null,
          playUrl: null,
          contentType: "video",
          subtitle: null,
        }));
    }

    return {
      videoDetails: response.data,
      episodes: episodesResponse.data?.list || [],
      totalEpisodes: episodesResponse.data?.total || 0,
      recommendations: recommendations,
    };
  } catch (error) {
    console.error(`Failed to load data for video ID ${videoId}:`, error);
    // Re-throw response errors
    if (error instanceof Response) {
      throw error;
    }
    // Throw a generic server error for other issues
    throw new Response("Internal Server Error", {
      status: 500,
      statusText: "Failed to load video details.",
    });
  }
}

// --- Server-side loader ---
export async function loader({ params, request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request);
  return await getData(params, locale);
}

// --- Client-side loader ---
export async function clientLoader({ params }: ClientLoaderFunctionArgs) {
  return await getData(params);
}

const EPISODES_VISIBLE_DEFAULT = 24;

export default function VideoDetailPage() {
  const { t } = useTranslation();
  const { videoId: routeVideoId } = useParams();
  const [showMoreSynopsis, setShowMoreSynopsis] = useState(false);
  const [areAllEpisodesShown, setAreAllEpisodesShown] = useState(false);
  const navigate = useNavigate();
  const { videoDetails, episodes, totalEpisodes, recommendations } =
    useLoaderData<typeof clientLoader>();
  const { navigateToVideo, navigateToVideoPlay } = useVideoNavigation();
  const { isLoggedIn } = useUser();

  // Use authenticated post list when user is logged in
  const { episodes: authenticatedEpisodes } = useAuthenticatedPostList({
    collectionId: routeVideoId || "",
    enabled: isLoggedIn && !!routeVideoId,
  });

  // Use authenticated episodes if user is logged in and data is available, otherwise use loader data
  const currentEpisodes =
    isLoggedIn && authenticatedEpisodes.length > 0
      ? authenticatedEpisodes
      : episodes;

  // Use the picture from API as poster image
  const posterImageUrl =
    videoDetails.picture ||
    "https://www.claudeusercontent.com/api/placeholder/1600/900"; // Fallback placeholder

  // Get the synopsis or introduction text
  const synopsis = videoDetails.introduce ?? t("video.noDescription");
  const displaySynopsis = showMoreSynopsis
    ? synopsis
    : synopsis.length > 200
    ? `${synopsis.substring(0, 200)}...`
    : synopsis;

  const handlePlayClick = (episodeId?: string) => {
    // If an episodeId is provided, use it.
    // Otherwise, try to use the first episode's ID from the list.
    // If no episodes are available, fall back to the main video's ID (from videoDetails).

    if (routeVideoId) {
      navigateToVideoPlay(routeVideoId, episodeId);
    } else {
      // Fallback if no targetId can be determined (should ideally not happen if videoDetails exists)
      console.warn("No valid video or episode ID found for playback.");
    }
  };

  // Helper function to truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return "";
    return text.length > maxLength
      ? `${text.substring(0, maxLength)}...`
      : text;
  };

  // Mock callbacks have been removed as they were unused

  if (!videoDetails) {
    return (
      <div className="bg-black text-white min-h-screen flex items-center justify-center">
        <p>{t("video.notFound")}</p>
      </div>
    );
  }

  const [initialEpisodes, setInitialEpisodes] = useState<EpisodeItem[]>(
    currentEpisodes.slice(0, EPISODES_VISIBLE_DEFAULT)
  );

  useEffect(() => {
    if (areAllEpisodesShown) {
      setInitialEpisodes(currentEpisodes);
    } else {
      setInitialEpisodes(currentEpisodes.slice(0, EPISODES_VISIBLE_DEFAULT));
    }
  }, [areAllEpisodesShown, currentEpisodes]);

  return (
    <>
      <div className="bg-body-bg text-white min-h-screen relative">
        {/* Back Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/")}
          className="absolute mt-safe top-4 left-4 z-30 p-2 rounded-full hover:bg-black/70"
          leftIcon={<ArrowLeft size={24} />}
          aria-label={t("nav.goBack")}
        />

        {/* Hero Banner with Content Details */}
        <div className="relative w-full">
          <div className="container mx-auto px-4 py-6 flex flex-col md:flex-row gap-6 relative z-10">
            {/* Poster Image */}
            <div className="w-full md:w-1/3 lg:w-1/4 flex-shrink-0">
              <div className="aspect-[2/3] rounded-lg overflow-hidden bg-gray-800 shadow-lg">
                <img
                  src={posterImageUrl}
                  alt={videoDetails.title}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Content Info */}
            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold mb-2">
                {videoDetails.title}
              </h1>

              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center">
                  <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                  <span className="ml-1 text-sm font-medium">7.5</span>
                </div>
                <span className="text-gray-400 text-sm">|</span>
                <span className="text-sm text-gray-300">
                  {t("video.season")} 1
                </span>
                <span className="text-gray-400 text-sm">|</span>
                <span className="text-sm text-gray-300">
                  {totalEpisodes} {t("common.episodesCap")}
                </span>
                <span className="text-gray-400 text-sm">|</span>
                <span className="text-sm text-gray-300">2023/08/23</span>
              </div>

              <div className="flex gap-2 mb-4">
                {videoDetails.topic &&
                  videoDetails.topic.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-[#FFFFFF4D] text-white text-sm px-3 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}

                {videoDetails.vip && (
                  <span className="px-3 py-1 bg-yellow-700 rounded-full text-xs">
                    VIP
                  </span>
                )}
              </div>

              <p className="text-sm text-gray-300 mb-6 line-clamp-3">
                {displaySynopsis}
                {synopsis.length > 200 && (
                  <button
                    className="text-blue-400 hover:underline ml-1"
                    onClick={() => setShowMoreSynopsis(!showMoreSynopsis)}
                  >
                    {showMoreSynopsis
                      ? t("video.showLess")
                      : t("video.readMore")}
                  </button>
                )}
              </p>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 mt-4">
                <Button
                  variant="primary"
                  onClick={() => handlePlayClick()}
                  leftIcon={<PlayCircle size={20} />}
                >
                  {t("video.playNow")}
                </Button>
                {routeVideoId && (
                  <AddToWatchlistButton
                    videoId={routeVideoId}
                    variant="default"
                    useBookmarkIcon={true}
                    onToggle={(isInWatchlist) => {
                      console.log(
                        `Video ${routeVideoId} ${
                          isInWatchlist ? "added to" : "removed from"
                        } watchlist`
                      );
                    }}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black to-black/60 z-0"></div>
        </div>

        {/* Episode List */}
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">
              {t("episode.episodeList")} ({totalEpisodes}{" "}
              {t("common.episodesCap")})
            </h2>
            {currentEpisodes.length > EPISODES_VISIBLE_DEFAULT && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAreAllEpisodesShown(!areAllEpisodesShown)}
                className="text-gray-400 hover:text-white flex items-center"
              >
                {areAllEpisodesShown
                  ? t("video.showLess")
                  : t("episode.allEpisodes")}
                {areAllEpisodesShown ? (
                  <ChevronUp size={18} className="ml-1" />
                ) : (
                  <ChevronDown size={18} className="ml-1" />
                )}
              </Button>
            )}
          </div>

          <div className="grid grid-cols-5 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-2">
            {[...initialEpisodes]
              .sort((a, b) => {
                const aNum = parseInt(a.title.replace(".mp4", ""), 10);
                const bNum = parseInt(b.title.replace(".mp4", ""), 10);
                return aNum - bNum;
              })
              .map((episode, index) => {
                // For authenticated users, use the VIP status from authenticated data
                // For non-authenticated users, use the original episode data
                const isLocked = isLoggedIn
                  ? episode.vip || episode.gold > 0 // Check both VIP and gold requirements
                  : episode.vip;

                return (
                  <button
                    key={`initial-${episode.id}`}
                    disabled={isLocked}
                    className={cn(
                      episodeButtonStyles({
                        variant: isLocked ? "locked" : "default",
                      })
                    )}
                    onClick={() => !isLocked && handlePlayClick(episode.id)}
                    title={
                      episode.title || `${t("episode.episode")} ${index + 1}`
                    }
                  >
                    {/* remove the extension */}
                    {t("episode.ep")} {episode.title.replace(".mp4", "")}
                    {isLocked && (
                      <span className="absolute top-0 right-0 text-xs bg-brand-red rounded-tr-md rounded-bl-md">
                        <Lock size={12} className="  text-white m-[2px]" />
                      </span>
                    )}
                  </button>
                );
              })}
          </div>
        </div>

        {/* Recommendations */}
        <div className="container mx-auto px-4 py-8 pb-24">
          <h2 className="text-xl font-bold mb-4">
            {t("video.recommendationsForYou")}
          </h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {recommendations.map((item) => (
              <div
                key={item.id}
                className="cursor-pointer group"
                onClick={() => navigateToVideo(item.id)}
              >
                <div className="aspect-[2/3] rounded-md overflow-hidden bg-gray-800 shadow-lg transition-transform duration-200 ease-in-out group-hover:scale-105">
                  <img
                    src={
                      item.picture ||
                      "https://www.claudeusercontent.com/api/placeholder/400/600"
                    }
                    alt={item.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="mt-2 text-sm font-medium line-clamp-2">
                  {truncateText(item.title, 30)}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}

export const meta: MetaFunction<typeof clientLoader> = ({ data }) => {
  if (!data || typeof data !== "object" || !("videoDetails" in data)) {
    return [{ title: "Video Details | SnapDrama" }];
  }

  const videoData = data as {
    videoDetails: { title: string; introduce: string };
  };
  console.log("videoData", videoData);
  return [
    {
      title: `${videoData.videoDetails.title} | SnapDrama`,
      description: videoData.videoDetails.introduce,
    },
  ];
};
