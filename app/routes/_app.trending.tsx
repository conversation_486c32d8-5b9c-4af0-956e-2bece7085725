import { useLoaderData } from "@remix-run/react";
import type { ContentItem, SearchPostPayload } from "~/types/index";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import { createBaseApi } from "~/utils/base-service";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";

import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "~/components/ui/Tabs";
import { useVideoNavigation } from "~/hooks/useVideoNavigation";
import { Star, FolderKanban } from "lucide-react";

// Loader function using searchPosts API
export async function clientLoader(): Promise<
  | {
      thisWeek: ContentItem[];
      thisMonth: ContentItem[];
    }
  | { error: string }
> {
  try {
    const api = createBaseApi();

    // Create different search payloads for different categories
    const basePayload: SearchPostPayload = {
      title: "",
      topicId: "",
      country: "",
      collectionId: "",
      pageNumber: 1,
      pageSize: 16, // 8 items per column for desktop
    };

    // Fetch trending content for this week
    const weeklyResponse = await api.searchPosts({
      ...basePayload,
      pageSize: 8,
    });

    // Fetch trending content for this month (different page or parameters)
    const monthlyResponse = await api.searchPosts({
      ...basePayload,
      pageNumber: 2,
      pageSize: 8,
    });

    if (!weeklyResponse.ok || !monthlyResponse.ok) {
      throw new Error("Failed to fetch trending data");
    }

    // Map API responses to ContentItems
    const weeklyItems = mapPostItemsToContentItems(
      weeklyResponse.data?.list || []
    );
    const monthlyItems = mapPostItemsToContentItems(
      monthlyResponse.data?.list || []
    );

    const data = {
      thisWeek: weeklyItems,
      thisMonth: monthlyItems,
    };

    return data;
  } catch (error) {
    console.error("Failed to fetch trending data:", error);
    return {
      thisWeek: [],
      thisMonth: [],
      error: "Failed to load data",
    };
  }
}

// Trending Item Card with Ranking Number
interface TrendingItemCardProps {
  item: ContentItem;
  rank: number;
}

const TrendingItemCard = ({ item, rank }: TrendingItemCardProps) => {
  const { navigateToVideo } = useVideoNavigation();

  const handleClick = () => {
    navigateToVideo(item.id);
  };

  return (
    <div
      className="flex p-3 rounded-lg cursor-pointer group hover:bg-gray-800/70 transition-colors duration-200"
      onClick={handleClick}
    >
      {/* Ranking Number */}
      <div className="flex items-center mr-4">
        <div
          className="text-[5rem] lg:text-[8rem] lg:w-24 w-12 font-bold text-stroke-2 text-stroke-gray-700 text-transparent relative z-0 select-none shrink-0"
          style={{ WebkitTextStroke: "2px #4A5568" }}
        >
          {rank}
        </div>
      </div>

      {/* Content Item */}
      <div className="w-24 sm:w-28 h-36 sm:h-42 rounded-md overflow-hidden bg-gray-700 shrink-0 shadow-md">
        <img
          src={item.thumbnailUrl}
          alt={item.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
      </div>

      <div className="ml-4 flex flex-col text-white flex-grow min-w-0">
        <h4 className="text-base sm:text-lg font-semibold truncate mb-1 group-hover:text-gray-200">
          {item.name}
        </h4>
        <div className="text-xs sm:text-sm text-gray-400 mb-1 space-x-1">
          <span>
            Season <span className="text-red-500 font-medium">1</span>
          </span>
          {item.episodes && (
            <>
              <span>•</span>
              <span>
                Episodes{" "}
                <span className="text-red-500 font-medium">
                  {item.episodes}
                </span>
              </span>
            </>
          )}
        </div>
        {item.genre && (
          <div className="text-xs sm:text-sm text-gray-400 mb-1 flex items-center">
            <FolderKanban size={14} className="mr-1.5 text-gray-500 shrink-0" />
            <span className="truncate">
              {Array.isArray(item.genre) ? item.genre.join(", ") : item.genre}
            </span>
          </div>
        )}
        {item.rating && (
          <div className="text-xs sm:text-sm text-yellow-400 flex items-center">
            <Star size={14} className="mr-1 fill-yellow-400 shrink-0" />
            <span>{item.rating.toFixed(1)}</span>
          </div>
        )}
      </div>
    </div>
  );
};

// Trending Section Component
interface TrendingSectionProps {
  title: string;
  items: ContentItem[];
}

const TrendingSection = ({ title, items }: TrendingSectionProps) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="rounded-lg overflow-hidden">
      <div className=" py-4">
        <h2 className="text-white text-xl md:text-2xl font-bold">{title}</h2>
      </div>
      <div className="divide-y divide-gray-700">
        {items.map((item, index) => (
          <TrendingItemCard key={item.id} item={item} rank={index + 1} />
        ))}
      </div>
    </div>
  );
};

// Main Trending Component
export default function Trending() {
  const data = useLoaderData<typeof clientLoader>();
  const { t } = useTranslation();
  const { isDesktop } = useMediaQuery();

  if (data && typeof data === "object" && "error" in data && data.error) {
    return (
      <div className="bg-body-bg min-h-screen text-white flex items-center justify-center">
        <p>Error loading content: {data.error}</p>
      </div>
    );
  }

  const hasData = (
    d: any
  ): d is {
    thisWeek: ContentItem[];
    thisMonth: ContentItem[];
  } => {
    return (
      d &&
      typeof d === "object" &&
      !("error" in d) &&
      Array.isArray(d.thisWeek) &&
      Array.isArray(d.thisMonth)
    );
  };

  if (!hasData(data)) {
    return (
      <div className="bg-body-bg min-h-screen text-white flex items-center justify-center">
        <p>Loading or invalid data structure...</p>
      </div>
    );
  }

  return (
    <div className="bg-body-bg min-h-screen text-white">
      {/* Main Content */}
      <div className="pt-16 pb-16 md:pb-8">
        {/* Content Layout */}
        <div className="px-4 md:px-8">
          {isDesktop ? (
            // Desktop: Two-column layout with page title
            <>
              <div className="mb-8">
                <h1 className="text-white text-3xl md:text-4xl font-bold">
                  {t("nav.trending") || "Trending"}
                </h1>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <TrendingSection
                  title={t("trending.thisWeek") || "Trending This Week"}
                  items={data.thisWeek}
                />
                <TrendingSection
                  title={t("trending.thisMonth") || "Trending This Month"}
                  items={data.thisMonth}
                />
              </div>
            </>
          ) : (
            // Mobile: Title and tabs on the same row
            <Tabs defaultValue="thisWeek" className="w-full">
              {/* Title and Tabs Row */}
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-white text-2xl font-bold">
                  {t("nav.trending") || "Trending"}
                </h1>
                <TabsList className="bg-gray-800 border border-gray-700">
                  <TabsTrigger
                    value="thisWeek"
                    className="text-gray-300 data-[state=active]:bg-red-500 data-[state=active]:text-white text-xs px-3 py-1"
                  >
                    {t("trending.weekly") || "This Week"}
                  </TabsTrigger>
                  <TabsTrigger
                    value="thisMonth"
                    className="text-gray-300 data-[state=active]:bg-red-500 data-[state=active]:text-white text-xs px-3 py-1"
                  >
                    {t("trending.monthly") || "This Month"}
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="thisWeek" className="mt-0">
                <TrendingSection title="" items={data.thisWeek} />
              </TabsContent>

              <TabsContent value="thisMonth" className="mt-0">
                <TrendingSection title="" items={data.thisMonth} />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  );
}
