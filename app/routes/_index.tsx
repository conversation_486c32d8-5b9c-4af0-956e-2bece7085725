import {
  use<PERSON>oaderD<PERSON>,
  ClientLoaderFunctionArgs,
  MetaFunction,
} from "@remix-run/react";

import type { LoaderFunctionArgs } from "@remix-run/node";
import type {
  ContentItem,
  HomeIndexItem,
  HomeIndexResponse,
  TopicItem,
} from "~/types/index"; // Updated imports

import { useTranslation } from "react-i18next";
import FeaturedContent from "~/components/FeaturedContent";
import PopularOfTheWeekRow from "~/components/PopularOfTheWeekRow";
import LastWatchedRow from "~/components/LastWatchedRow";
import TrendingRow from "~/components/TrendingRow";
import NewReleaseRow from "~/components/NewReleaseRow";
import GenreRow from "~/components/GenreRow";
import { createBaseApi } from "~/utils/base-service";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";

import MovieCard from "~/components/ui/MovieCard";

import i18next from "~/i18next.server";

// Define props for sections if needed later, for now, structure matches existing
// interface SectionProps {
//   title: string;
//   items: ContentItem[];
// }

interface SectionData {
  items: ContentItem[];
  title: string;
}

interface LoaderData {
  featured: SectionData;
  popularOfTheWeek: SectionData;
  lastWatched: ContentItem[] | null;
  popular: SectionData;
  trending: SectionData;
  watchAgain: ContentItem[];
  newReleases: SectionData;
  topics: TopicItem[];
  error?: string;
}

// to be used in loader and clientLoader
async function getData(locale?: string) {
  try {
    const apiResponse: HomeIndexResponse = await createBaseApi(
      locale
    ).getHomeIndex();

    // Initialize categorized content with titles based on sortValue
    let featured: SectionData = { items: [], title: "Featured" }; // sortValue: 1
    let popularOfTheWeek: SectionData = {
      items: [],
      title: "Popular of the Week",
    }; // sortValue: 2
    let trending: SectionData = { items: [], title: "Trending" }; // sortValue: 3
    let newReleases: SectionData = { items: [], title: "New Releases" }; // sortValue: 4
    let popular: SectionData = { items: [], title: "Popular" }; // sortValue: 5
    let allContentItems: ContentItem[] = [];

    if (apiResponse.ok && apiResponse.data) {
      // Sort sections by sortValue to ensure consistent processing
      const sortedSections = apiResponse.data.sort(
        (a, b) => a.sortValue - b.sortValue
      );

      sortedSections.forEach((section: HomeIndexItem) => {
        if (section.list && Array.isArray(section.list)) {
          const sectionItems: ContentItem[] = mapPostItemsToContentItems(
            section.list
          );

          // Add to all content items
          allContentItems.push(...sectionItems);

          // Categorize based on sortValue and use the API title
          switch (section.sortValue) {
            case 1:
              featured = { items: sectionItems, title: section.title };
              break;
            case 2:
              popularOfTheWeek = { items: sectionItems, title: section.title };
              break;
            case 3:
              trending = { items: sectionItems, title: section.title };
              break;
            case 4:
              newReleases = { items: sectionItems, title: section.title };
              break;
            case 5:
              popular = { items: sectionItems, title: section.title };
              break;
            default:
              // For any other sortValue, add to popular as fallback
              popular.items.push(...sectionItems);
              break;
          }
        }
      });
    }

    const data: LoaderData = {
      featured,
      popularOfTheWeek,
      lastWatched: featured.items.slice(0, 5), // Use featured content for last watched as fallback
      popular,
      trending,
      watchAgain: popular.items.slice(0, 10), // Use popular content for watch again as fallback
      newReleases,
      topics: [],
    };

    // Fetch topics for genres
    let topics: TopicItem[] = [];
    try {
      const topicResp = await createBaseApi(locale).getHomeTopic();
      if (topicResp.ok && topicResp.data) {
        topics = topicResp.data;
      }
    } catch (err) {
      console.error("Failed to fetch topics:", err);
    }
    return { ...data, topics };
  } catch (error) {
    console.error("Failed to fetch home index data:", error);

    const errorData: LoaderData = {
      featured: { items: [], title: "Featured" },
      popularOfTheWeek: { items: [], title: "Popular of the Week" },
      lastWatched: [],
      popular: { items: [], title: "Popular" },
      trending: { items: [], title: "Trending" },
      watchAgain: [],
      newReleases: { items: [], title: "New Releases" },
      topics: [],
      error: "Failed to load data",
    };
    return errorData;
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request);
  return getData(locale);
}

export async function clientLoader({}: ClientLoaderFunctionArgs) {
  return await getData();
}

// Content Row Component (standard grid)
interface ContentRowProps {
  title: string;
  items: ContentItem[];
}

const ContentRow = ({ title, items }: ContentRowProps) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-white text-lg font-semibold mb-3 px-4">{title}</h3>
      <div className="grid grid-cols-3 gap-x-3 gap-y-5 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 px-4">
        {items.map((item) => (
          <MovieCard key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

// Main App Component
export default function Component() {
  const data = useLoaderData<typeof clientLoader>();
  const { t } = useTranslation();

  if (data && typeof data === "object" && "error" in data && data.error) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center p-4">
        <p className="text-center">
          Error loading content: {data.error as string}
        </p>
      </div>
    );
  }

  const hasData = (
    d: any
  ): d is {
    featured: SectionData;
    popularOfTheWeek: SectionData;
    lastWatched: ContentItem[];
    popular: SectionData;
    trending: SectionData;
    watchAgain: ContentItem[];
    newReleases: SectionData;
    topics: TopicItem[];
  } => {
    return (
      d &&
      typeof d === "object" &&
      !("error" in d) &&
      d.featured &&
      typeof d.featured === "object" &&
      Array.isArray(d.featured.items) &&
      d.popularOfTheWeek &&
      typeof d.popularOfTheWeek === "object" &&
      Array.isArray(d.popularOfTheWeek.items) &&
      d.popular &&
      typeof d.popular === "object" &&
      Array.isArray(d.popular.items) &&
      d.trending &&
      typeof d.trending === "object" &&
      Array.isArray(d.trending.items) &&
      Array.isArray(d.watchAgain) &&
      d.newReleases &&
      typeof d.newReleases === "object" &&
      Array.isArray(d.newReleases.items) &&
      d.topics &&
      typeof d.topics === "object" &&
      Array.isArray(d.topics)
    );
  };

  if (!hasData(data)) {
    return (
      <div className="bg-black min-h-screen text-white flex items-center justify-center p-4">
        <p className="text-center">Loading or invalid data structure...</p>
      </div>
    );
  }

  return (
    <>
      {data.featured.items.length > 0 && (
        <FeaturedContent items={data.featured.items.slice(0, 5)} />
      )}
      <div className="pb-20 space-y-10 max-w-screen-2xl mx-auto mt-10">
        {data.newReleases.items.length > 0 && (
          <NewReleaseRow
            title={data.newReleases.title}
            items={data.newReleases.items}
          />
        )}
        {data.popularOfTheWeek.items.length > 0 && (
          <PopularOfTheWeekRow
            title={data.popularOfTheWeek.title}
            items={data.popularOfTheWeek.items}
          />
        )}

        <ContentRow
          title={data.popular.title}
          items={data.popular.items.slice(0, 12)}
        />
        <ContentRow title={t("videoFeed.watchAgain")} items={data.watchAgain} />
        <GenreRow
          title={t("genres.title")}
          topics={data.topics}
          initialItems={data.popular.items}
        />
        <div className="flex lg:flex-row flex-col gap-y-10">
          <div className="lg:w-1/2">
            {data.lastWatched && (
              <LastWatchedRow
                title={t("home.lastWatched")}
                items={data.lastWatched}
              />
            )}
          </div>
          <div className="lg:w-1/2">
            {/* Use TrendingRow for the trending section */}
            {data.trending.items.length > 0 && (
              <TrendingRow
                title={data.trending.title}
                items={data.trending.items}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export const meta: MetaFunction = () => {
  return [
    { title: "Home | Snapdrama" },
    {
      property: "og:title",
      content: "Home | Snapdrama",
    },
    {
      name: "description",
      content:
        "Snapdrama is a mobile streaming platform that specializes in short-form dramas, films, and series spanning a wide range of genres. Designed for quick and engaging entertainment, it's perfect for watching during short breaks, daily commutes, or any spare moments throughout the day.",
    },
    {
      name: "keywords",
      content: "short videos, drama, series, mobile streaming, entertainment",
    },
  ];
};
