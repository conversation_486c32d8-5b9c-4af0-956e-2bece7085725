import { MetaFunction } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import LoginComponent from "~/components/auth/Login";

export const meta: MetaFunction = () => {
  return [
    { title: "Login | SnapDrama" },
    { name: "description", content: "Login to SnapDrama to continue." },
  ];
};

export default function LoginPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen text-white flex flex-col relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-top z-0"
        style={{
          backgroundImage: "url('/images/login-bg.png')",
        }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
      </div>
      <div className="z-50 mt-10">
        <div className="flex flex-col items-center text-center">
          <img
            src="/logo.png"
            alt="SnapDrama Logo"
            className="h-32 sm:h-20 mb-2"
          />
        </div>
        <h2 className="text-xl sm:text-2xl font-semibold text-center text-white">
          {t("auth.login.title")}
        </h2>
      </div>
      <LoginComponent />
    </div>
  );
}
