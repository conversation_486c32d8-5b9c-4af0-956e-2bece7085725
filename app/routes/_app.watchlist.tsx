import { useMemo } from "react";
import { MetaFunction } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { Trash2, History } from "lucide-react";

import MovieCard from "~/components/ui/MovieCard";
import Button from "~/components/ui/Button";
import type { ContentItem } from "~/types/index";
import { useMediaQuery } from "~/hooks/useMediaQuery";
import { useUser } from "~/context/auth-context";
import { useCollect, useUpdateRel } from "~/hooks/usePraiseCollect";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";

// Mobile watchlist item component
interface MobileWatchlistItemProps {
  item: ContentItem;
  onRemove: (id: string) => void;
}

const MobileWatchlistItem = ({ item, onRemove }: MobileWatchlistItemProps) => {
  const { t } = useTranslation();

  return (
    <div className="flex items-center space-x-3 p-3 bg-body-bg rounded-lg">
      <div className="w-16 h-24 flex-shrink-0">
        <img
          src={item.thumbnailUrl}
          alt={item.name}
          className="w-full h-full object-cover rounded"
          loading="lazy"
        />
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="text-white font-medium text-sm truncate">{item.name}</h3>
        <p className="text-gray-400 text-xs mt-1 line-clamp-2">
          {item.description}
        </p>
        <p className="text-gray-400 text-xs">
          {item.genre?.join(" • ") || "Unknown"}
        </p>
        {item.rating && (
          <div className="flex items-center mt-1">
            <span className="text-yellow-400 text-xs">★</span>
            <span className="text-white text-xs ml-1">{item.rating}</span>
          </div>
        )}
      </div>
      <button
        onClick={() => onRemove(item.id)}
        className="p-2 text-red-500 hover:text-red-400 transition-colors"
        aria-label={t("watchlist.removeItem", "Remove from watchlist")}
      >
        <Trash2 size={16} />
      </button>
    </div>
  );
};

export default function WatchlistPage() {
  const { data: collectItems = [] } = useCollect();
  const { t } = useTranslation();
  const updateRelMutation = useUpdateRel();

  const { isDesktop } = useMediaQuery();
  const { userInfo, isLoggedIn } = useUser();

  // Convert CollectItems to ContentItems using unified mapping function
  const contentItems = useMemo(() => {
    return mapPostItemsToContentItems(collectItems);
  }, [collectItems]);

  const handleRemoveItem = async (id: string) => {
    // Check if user is logged in
    if (!isLoggedIn || !userInfo) {
      console.log("User must be logged in to remove items");
      return;
    }

    try {
      await updateRelMutation.mutateAsync({
        userId: userInfo.id,
        postId: id,
        type: "collect",
        value: false, // Remove from collection
      });
      console.log("Item removed from watchlist");
    } catch (error) {
      console.error("Error removing item:", error);
    }
  };

  const handleDeleteAll = async () => {
    // Check if user is logged in
    if (!isLoggedIn || !userInfo) {
      console.log("User must be logged in to clear watchlist");
      return;
    }

    try {
      // Remove all items one by one using the mutation hook
      const removePromises = collectItems.map((item) =>
        updateRelMutation.mutateAsync({
          userId: userInfo.id,
          postId: item.id,
          type: "collect",
          value: false,
        })
      );

      await Promise.all(removePromises);
      console.log("All items removed from watchlist");
    } catch (error) {
      console.error("Error clearing watchlist:", error);
    }
  };

  const isEmpty = contentItems.length === 0;

  return (
    <div className="bg-body-bg pt-16 pb-20">
      <div className="max-w-screen-2xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <History size={24} className="text-white" />
            <h1 className="text-white text-xl md:text-2xl font-bold">
              {isDesktop
                ? t("watchlist.title", "My Watchlist")
                : t("watchlist.title", "My Watchlist")}
            </h1>
            {!isEmpty && (
              <span className="text-gray-400 text-sm">
                {contentItems.length} {t("watchlist.dramas", "Dramas")}
              </span>
            )}
          </div>

          {!isEmpty && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDeleteAll}
              className="text-gray-400 hover:text-white"
              leftIcon={<Trash2 size={16} />}
            >
              {t("watchlist.deleteAll", "Delete All")}
            </Button>
          )}
        </div>

        {/* Content */}
        {isEmpty ? (
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <div className="w-24 h-24 bg-gray-800 rounded-full flex items-center justify-center mb-4">
              <History size={32} className="text-gray-600" />
            </div>
            <h2 className="text-white text-lg font-semibold mb-2">
              {t("watchlist.empty.title", "Your watchlist is empty")}
            </h2>
            <p className="text-gray-400 text-sm max-w-md">
              {t(
                "watchlist.empty.description",
                "Add shows and movies to your watchlist to watch them later."
              )}
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Grid View */}
            {isDesktop ? (
              <div className="grid grid-cols-3 gap-x-4 gap-y-6 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 2xl:grid-cols-8">
                {contentItems.map((item) => (
                  <div key={item.id} className="relative group">
                    <MovieCard
                      item={item}
                      titleSize="sm"
                      withAccessibility={true}
                    />
                    <button
                      onClick={() => handleRemoveItem(item.id)}
                      className="absolute top-2 right-2 p-1.5 bg-black bg-opacity-70 rounded-full text-red-500 hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity"
                      aria-label={t(
                        "watchlist.removeItem",
                        "Remove from watchlist"
                      )}
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              /* Mobile List View */
              <div className="space-y-3">
                {contentItems.map((item) => (
                  <MobileWatchlistItem
                    key={item.id}
                    item={item}
                    onRemove={handleRemoveItem}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export const meta: MetaFunction = () => {
  return [
    { title: "My Watchlist | Snapdrama" },
    {
      property: "og:title",
      content: "My Watchlist | Snapdrama",
    },
    {
      name: "description",
      content:
        "Manage your personal watchlist of shows and movies on Snapdrama.",
    },
  ];
};
