import { Outlet } from "@remix-run/react";
import AppHeader from "~/components/AppHeader";
import Footer from "~/components/Footer";
import MobileFooter from "~/components/MobileFooter";
import { useMediaQuery } from "~/hooks/useMediaQuery";

/**
 * Layout component that includes <PERSON><PERSON>Head<PERSON> and Footer for most app pages
 * This layout is used for all routes that need the standard app navigation
 */
export default function AppLayout() {
  const { isDesktop } = useMediaQuery();

  return (
    <div className="flex flex-col min-h-screen">
      <AppHeader />
      <main className="flex-grow">
        <Outlet />
      </main>
      {isDesktop ? <Footer /> : <MobileFooter />}
    </div>
  );
}
