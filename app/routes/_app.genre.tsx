import { useState, useEffect } from "react";
import {
  useSearchParams,
  useLoaderData,
  ClientLoaderFunctionArgs,
  MetaFunction,
} from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";

import MovieCard from "~/components/ui/MovieCard";
import { useTranslation } from "react-i18next";
import type {
  ContentItem,
  HomeIndexItem,
  HomeIndexListItem,
  HomeIndexResponse,
} from "~/types/index";
import { createBaseApi } from "~/utils/base-service";
import { mapPostItemsToContentItems } from "~/utils/dataMappers";
import i18next from "~/i18next.server";

// Function to fetch and process data from getHomeIndex API
async function getData(locale?: string) {
  try {
    const apiResponse: HomeIndexResponse = await createBaseApi(
      locale
    ).getHomeIndex();

    // Initialize categorized content arrays with titles based on sortValue
    let featured: { items: ContentItem[]; title: string } = {
      items: [],
      title: "Featured",
    }; // sortValue: 1
    let popularOfTheWeek: { items: ContentItem[]; title: string } = {
      items: [],
      title: "Popular of the Week",
    }; // sortValue: 2
    let trending: { items: ContentItem[]; title: string } = {
      items: [],
      title: "Trending",
    }; // sortValue: 3
    let newReleases: { items: ContentItem[]; title: string } = {
      items: [],
      title: "New Releases",
    }; // sortValue: 4
    let popular: { items: ContentItem[]; title: string } = {
      items: [],
      title: "Popular",
    }; // sortValue: 5
    let allContentItems: ContentItem[] = [];
    let availableGenres: string[] = [];

    if (apiResponse.ok && apiResponse.data) {
      // Sort sections by sortValue to ensure consistent processing
      const sortedSections = apiResponse.data.sort(
        (a, b) => a.sortValue - b.sortValue
      );

      sortedSections.forEach((section: HomeIndexItem) => {
        // Collect unique genres from the country field
        if (section.country && !availableGenres.includes(section.country)) {
          availableGenres.push(section.country);
        }

        if (section.list && Array.isArray(section.list)) {
          const sectionItems: ContentItem[] = mapPostItemsToContentItems(
            section.list
          );
          // Add to all content items
          allContentItems.push(...sectionItems);

          // Categorize based on sortValue and use the API title
          switch (section.sortValue) {
            case 1:
              featured = { items: sectionItems, title: section.title };
              break;
            case 2:
              popularOfTheWeek = { items: sectionItems, title: section.title };
              break;
            case 3:
              trending = { items: sectionItems, title: section.title };
              break;
            case 4:
              newReleases = { items: sectionItems, title: section.title };
              break;
            case 5:
              popular = { items: sectionItems, title: section.title };
              break;
            default:
              // For any other sortValue, add to popular as fallback
              popular.items.push(...sectionItems);
              break;
          }
        }
      });
    }

    return {
      content: allContentItems,
      genres: availableGenres.sort(), // Sort genres alphabetically
      categorizedContent: {
        featured,
        popularOfTheWeek,
        trending,
        newReleases,
        popular,
      },
      error: undefined,
    };
  } catch (error) {
    console.error("Failed to fetch home index data:", error);
    return {
      content: [],
      genres: [],
      categorizedContent: {
        featured: { items: [], title: "Featured" },
        popularOfTheWeek: { items: [], title: "Popular of the Week" },
        trending: { items: [], title: "Trending" },
        newReleases: { items: [], title: "New Releases" },
        popular: { items: [], title: "Popular" },
      },
      error: "Failed to load data",
    };
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  const locale = await i18next.getLocale(request);
  const url = new URL(request.url);
  const initialGenre = url.searchParams.get("genre");

  const data = await getData(locale);

  return {
    initialGenre,
    ...data,
  };
}

export async function clientLoader({ request }: ClientLoaderFunctionArgs) {
  const url = new URL(request.url);
  const initialGenre = url.searchParams.get("genre");

  const data = await getData();

  return {
    initialGenre,
    ...data,
  };
}

export default function GenrePage() {
  const { t } = useTranslation();
  const data = useLoaderData<typeof clientLoader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedGenre, setSelectedGenre] = useState<string | null>(
    data.initialGenre
  );

  // Update selected genre when URL changes
  useEffect(() => {
    const genreParam = searchParams.get("genre");
    setSelectedGenre(genreParam);
  }, [searchParams]);

  const handleGenreChange = (genre: string | null) => {
    setSelectedGenre(genre);
    if (genre) {
      setSearchParams({ genre });
    } else {
      setSearchParams({});
    }
  };

  // Filter content based on selected genre
  const filteredContent = selectedGenre
    ? data.content.filter((item) => item.genre?.includes(selectedGenre))
    : data.content;

  // Get content count for current filter
  const contentCount = filteredContent.length;

  // Handle error state
  if (data.error) {
    return (
      <div className="bg-main-bg">
        <div className="max-w-screen-2xl mx-auto px-4 py-6">
          <div className="text-center py-12">
            <p className="text-red-400 text-lg mb-2">
              {t("genres.error", "Error loading content")}
            </p>
            <p className="text-gray-500 text-sm">{data.error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-main-bg">
      <div className="max-w-screen-2xl mx-auto px-4 py-6">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-white text-2xl font-bold mb-2">
            {t("genres.title", "Browse by Genre")}
          </h1>
          <p className="text-gray-400 text-sm">
            {selectedGenre
              ? t("genres.showing", "Showing {{count}} {{genre}} titles", {
                  count: contentCount,
                  genre: selectedGenre,
                })
              : t(
                  "genres.showingAll",
                  "Showing {{count}} titles across all genres",
                  {
                    count: contentCount,
                  }
                )}
          </p>
        </div>

        {/* Categorized Content Sections - Only show when no genre is selected */}
        {!selectedGenre && data.categorizedContent && (
          <div className="space-y-8 mb-12">
            {/* Featured Content */}
            {data.categorizedContent.featured.items.length > 0 && (
              <div>
                <h2 className="text-white text-xl font-semibold mb-4">
                  {data.categorizedContent.featured.title}
                </h2>
                <div className="grid grid-cols-2 gap-x-3 gap-y-5 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                  {data.categorizedContent.featured.items
                    .slice(0, 10)
                    .map((item) => (
                      <MovieCard key={item.id} item={item} />
                    ))}
                </div>
              </div>
            )}

            {/* Popular of the Week */}
            {data.categorizedContent.popularOfTheWeek.items.length > 0 && (
              <div>
                <h2 className="text-white text-xl font-semibold mb-4">
                  {data.categorizedContent.popularOfTheWeek.title}
                </h2>
                <div className="grid grid-cols-2 gap-x-3 gap-y-5 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                  {data.categorizedContent.popularOfTheWeek.items
                    .slice(0, 10)
                    .map((item) => (
                      <MovieCard key={item.id} item={item} />
                    ))}
                </div>
              </div>
            )}

            {/* Trending */}
            {data.categorizedContent.trending.items.length > 0 && (
              <div>
                <h2 className="text-white text-xl font-semibold mb-4">
                  {data.categorizedContent.trending.title}
                </h2>
                <div className="grid grid-cols-2 gap-x-3 gap-y-5 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                  {data.categorizedContent.trending.items
                    .slice(0, 10)
                    .map((item) => (
                      <MovieCard key={item.id} item={item} />
                    ))}
                </div>
              </div>
            )}

            {/* New Releases */}
            {data.categorizedContent.newReleases.items.length > 0 && (
              <div>
                <h2 className="text-white text-xl font-semibold mb-4">
                  {data.categorizedContent.newReleases.title}
                </h2>
                <div className="grid grid-cols-2 gap-x-3 gap-y-5 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                  {data.categorizedContent.newReleases.items
                    .slice(0, 10)
                    .map((item) => (
                      <MovieCard key={item.id} item={item} />
                    ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Genre Filter Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 overflow-x-auto pb-2">
            {/* All Genres Tab */}
            <button
              onClick={() => handleGenreChange(null)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-150 ease-in-out whitespace-nowrap flex-shrink-0 ${
                selectedGenre === null
                  ? "bg-brand-red text-white"
                  : "bg-zinc-800 text-gray-300 hover:bg-zinc-700 hover:text-white"
              }`}
            >
              {t("genres.all", "All Genres")}
            </button>

            {/* Individual Genre Tabs */}
            {data.genres.map((genre: string) => (
              <button
                key={genre}
                onClick={() => handleGenreChange(genre)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-150 ease-in-out whitespace-nowrap flex-shrink-0 ${
                  selectedGenre === genre
                    ? "bg-slate-600 text-white"
                    : "bg-zinc-800 text-gray-300 hover:bg-zinc-700 hover:text-white"
                }`}
              >
                {t(`genres.${genre.toLowerCase()}`, genre)}
              </button>
            ))}
          </div>
        </div>

        {/* Content Grid */}
        <div className="mb-8">
          {filteredContent.length > 0 ? (
            <div className="grid grid-cols-3 gap-x-3 gap-y-5 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6">
              {filteredContent.map((item) => (
                <MovieCard key={item.id} item={item} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-400 text-lg mb-2">
                {t("genres.noContent", "No content found")}
              </p>
              <p className="text-gray-500 text-sm">
                {selectedGenre
                  ? t(
                      "genres.noContentGenre",
                      "No {{genre}} content available",
                      {
                        genre: selectedGenre,
                      }
                    )
                  : t("genres.noContentAll", "No content available")}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export const meta: MetaFunction = () => {
  return [
    { title: "Browse by Genre | Snapdrama" },
    {
      property: "og:title",
      content: "Browse by Genre | Snapdrama",
    },
    {
      name: "description",
      content:
        "Browse movies and shows by genre on Snapdrama. Find your favorite Action, Comedy, Drama, Horror, Romance, Sci-Fi, Thriller, and Documentary content.",
    },
    {
      name: "keywords",
      content:
        "genre, movies, shows, action, comedy, drama, horror, romance, sci-fi, thriller, documentary",
    },
  ];
};
